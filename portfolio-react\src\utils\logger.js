/**
 * Production-safe logging utility - UPDATED FOR COMPLETE BROWSER SECURITY
 *
 * This utility provides conditional logging that hides sensitive information
 * in production while maintaining helpful user feedback messages.
 *
 * IMPORTANT: All sensitive and debug logs are completely hidden in browser console
 */

// Force production mode for sensitive logging - never show sensitive info in browser console
// const isDevelopment = false; // Always treat as production for browser security
const isActualDevelopment = process.env.NODE_ENV === 'development';

/**
 * Log levels for different types of messages
 */
const LOG_LEVELS = {
  USER_FRIENDLY: 'user_friendly',    // Always show - user-facing messages
  DEBUG: 'debug',                    // Only show in development
  SENSITIVE: 'sensitive',            // Never show in production
  ERROR: 'error'                     // Always show errors
};

/**
 * Production-safe console logging
 * @param {string} message - The message to log
 * @param {string} level - The log level (USER_FRIENDLY, DEBUG, SENSITIVE, ERROR)
 * @param {any} data - Optional additional data to log
 */
export const safeLog = (message, level = LOG_LEVELS.DEBUG, data = null) => {
  switch (level) {
    case LOG_LEVELS.USER_FRIENDLY:
      // Always show user-friendly messages
      console.log(message, data || '');
      break;
      
    case LOG_LEVELS.ERROR:
      // Always show errors
      console.error(message, data || '');
      break;
      
    case LOG_LEVELS.DEBUG:
      // Only show in actual development mode (not in browser console)
      if (isActualDevelopment && typeof window === 'undefined') {
        console.log(message, data || '');
      }
      break;

    case LOG_LEVELS.SENSITIVE:
      // Never show sensitive information in browser console
      // Only in server-side development mode
      if (isActualDevelopment && typeof window === 'undefined') {
        console.log(`[SENSITIVE] ${message}`, data || '');
      }
      break;
      
    default:
      // Default to debug level - never show in browser
      if (isActualDevelopment && typeof window === 'undefined') {
        console.log(message, data || '');
      }
  }
};

/**
 * Production-safe console warning
 * @param {string} message - The warning message
 * @param {string} level - The log level
 * @param {any} data - Optional additional data
 */
export const safeWarn = (message, level = LOG_LEVELS.DEBUG, data = null) => {
  switch (level) {
    case LOG_LEVELS.USER_FRIENDLY:
    case LOG_LEVELS.ERROR:
      console.warn(message, data || '');
      break;
      
    case LOG_LEVELS.DEBUG:
      if (isActualDevelopment && typeof window === 'undefined') {
        console.warn(message, data || '');
      }
      break;

    case LOG_LEVELS.SENSITIVE:
      if (isActualDevelopment && typeof window === 'undefined') {
        console.warn(`[SENSITIVE] ${message}`, data || '');
      }
      break;

    default:
      if (isActualDevelopment && typeof window === 'undefined') {
        console.warn(message, data || '');
      }
  }
};

/**
 * Shorthand functions for common use cases
 */
export const logUserFriendly = (message, data = null) => 
  safeLog(message, LOG_LEVELS.USER_FRIENDLY, data);

export const logDebug = (message, data = null) => 
  safeLog(message, LOG_LEVELS.DEBUG, data);

export const logSensitive = (message, data = null) => 
  safeLog(message, LOG_LEVELS.SENSITIVE, data);

export const logError = (message, data = null) => 
  safeLog(message, LOG_LEVELS.ERROR, data);

export const warnUserFriendly = (message, data = null) => 
  safeWarn(message, LOG_LEVELS.USER_FRIENDLY, data);

export const warnDebug = (message, data = null) => 
  safeWarn(message, LOG_LEVELS.DEBUG, data);

export const warnSensitive = (message, data = null) => 
  safeWarn(message, LOG_LEVELS.SENSITIVE, data);

// Export log levels for use in other files
export { LOG_LEVELS };

const loggerUtils = {
  safeLog,
  safeWarn,
  logUserFriendly,
  logDebug,
  logSensitive,
  logError,
  warnUserFriendly,
  warnDebug,
  warnSensitive,
  LOG_LEVELS
};

export default loggerUtils;

// CommonJS compatibility for Node.js testing
if (typeof module !== 'undefined' && module.exports) {
  module.exports = loggerUtils;
}
