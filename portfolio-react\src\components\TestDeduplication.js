import React from 'react';
import { isValidPortfolioProject, PORTFOLIO_PROJECTS } from '../config/projectsConfig';

const TestDeduplication = () => {
  // Mock data that simulates all 7 portfolio projects
  const mockData = [
    { projectId: 'portfolio-0-3d-ecommerce', projectTitle: '3D Ecommerce' },
    { projectId: 'portfolio-1-will-be-deployed-soon', projectTitle: 'Will be deployed soon.' },
    { projectId: 'portfolio-2-professional-portfolio', projectTitle: 'Professional Portfolio' },
    { projectId: 'portfolio-3-will-be-deployed-soon', projectTitle: 'Will be deployed soon.' },
    { projectId: 'portfolio-4-available', projectTitle: 'Available' },
    { projectId: 'portfolio-5-will-be-deployed-soon', projectTitle: 'Will be deployed soon.' },
    { projectId: 'portfolio-6-experience-digital-banking-with-ai', projectTitle: 'Experience digital banking with AI ' },
  ];

  // Test configuration completeness
  const testConfiguration = () => {
    console.log('🧪 Testing configuration completeness:');
    console.log('PORTFOLIO_PROJECTS count:', PORTFOLIO_PROJECTS.length);
    console.log('Mock data count:', mockData.length);

    const configurationResults = mockData.map(project => {
      const isValid = isValidPortfolioProject(project.projectId, project.projectTitle);
      return {
        ...project,
        isValid,
        configFound: isValid
      };
    });

    console.log('Configuration test results:', configurationResults);
    const validCount = configurationResults.filter(r => r.isValid).length;
    const invalidCount = configurationResults.filter(r => !r.isValid).length;

    console.log(`✅ Valid projects: ${validCount}`);
    console.log(`❌ Invalid projects: ${invalidCount}`);

    return configurationResults;
  };

  const dedupeProjects = (projects) => {
    const seen = new Set();
    const duplicatesFound = [];
    
    const uniqueProjects = projects.filter(project => {
      // Create multiple unique identifiers to catch different types of duplicates
      const projectIdKey = project.projectId ? `id:${project.projectId}` : null;
      const projectTitleKey = project.projectTitle ? `title:${project.projectTitle.toLowerCase().trim()}` : null;
      const combinedKey = `${project.projectId || 'no-id'}-${project.projectTitle || 'no-title'}`;
      
      // Check if we've seen this project by any of these identifiers
      const isDuplicate = (projectIdKey && seen.has(projectIdKey)) ||
                        (projectTitleKey && seen.has(projectTitleKey)) ||
                        seen.has(combinedKey);
      
      if (isDuplicate) {
        duplicatesFound.push(project);
        return false;
      }
      
      // Add all identifiers to the seen set
      if (projectIdKey) seen.add(projectIdKey);
      if (projectTitleKey) seen.add(projectTitleKey);
      seen.add(combinedKey);
      
      return true;
    });
    
    return { uniqueProjects, duplicatesFound };
  };

  const testDeduplication = () => {
    console.log('🧪 Testing deduplication with mock data:');
    console.log('Original data:', mockData);
    
    const { uniqueProjects, duplicatesFound } = dedupeProjects(mockData);
    
    console.log('Unique projects:', uniqueProjects);
    console.log('Duplicates found:', duplicatesFound);
    console.log(`Removed ${duplicatesFound.length} duplicates, ${uniqueProjects.length} unique projects remain`);
    
    // Test validation
    const validProjects = uniqueProjects.filter(project => 
      isValidPortfolioProject(project.projectId, project.projectTitle)
    );
    
    console.log('Valid projects after filtering:', validProjects);
    
    return { uniqueProjects, duplicatesFound, validProjects };
  };

  const configResults = testConfiguration();
  const results = testDeduplication();

  return (
    <div style={{ padding: '20px', background: '#f0f0f0', margin: '20px' }}>
      <h2>Portfolio Projects Configuration Test</h2>

      <div>
        <h3>Configuration Test ({configResults.length} items tested):</h3>
        <pre>{JSON.stringify(configResults, null, 2)}</pre>
      </div>

      <div>
        <h3>PORTFOLIO_PROJECTS Configuration ({PORTFOLIO_PROJECTS.length} items):</h3>
        <pre>{JSON.stringify(PORTFOLIO_PROJECTS.map(p => ({
          id: p.id,
          title: p.title,
          projectIds: p.analyticsIdentifiers.projectIds,
          titles: p.analyticsIdentifiers.titles
        })), null, 2)}</pre>
      </div>

      <h2>Deduplication Test Results</h2>
      <div>
        <h3>Original Data ({mockData.length} items):</h3>
        <pre>{JSON.stringify(mockData, null, 2)}</pre>
      </div>
      <div>
        <h3>Unique Projects ({results.uniqueProjects.length} items):</h3>
        <pre>{JSON.stringify(results.uniqueProjects, null, 2)}</pre>
      </div>
      <div>
        <h3>Duplicates Removed ({results.duplicatesFound.length} items):</h3>
        <pre>{JSON.stringify(results.duplicatesFound, null, 2)}</pre>
      </div>
      <div>
        <h3>Valid Projects ({results.validProjects.length} items):</h3>
        <pre>{JSON.stringify(results.validProjects, null, 2)}</pre>
      </div>
    </div>
  );
};

export default TestDeduplication;
