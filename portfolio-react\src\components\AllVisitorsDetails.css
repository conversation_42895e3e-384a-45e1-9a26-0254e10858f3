/* All Visitors Details Page Styles */
.all-visitors-container {
  min-height: 100vh;

  position: relative;
  overflow: hidden;
  padding: 20px;
}

/* Complex background styling matching dashboard */
.all-visitors-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
  z-index: 1;
}

/* Floating particles animation */
.all-visitors-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(2px 2px at 20px 30px, rgba(255,255,255,0.3), transparent),
    radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.2), transparent),
    radial-gradient(1px 1px at 90px 40px, rgba(255,255,255,0.4), transparent),
    radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.2), transparent);
  background-repeat: repeat;
  background-size: 150px 100px;
  animation: float 20s infinite linear;
  z-index: 2;
}

@keyframes float {
  0% { transform: translate(0, 0); }
  100% { transform: translate(-150px, -100px); }
}

/* Content positioning */
.all-visitors-container > * {
  position: relative;
  z-index: 3;
}



/* Fixed Back Button Container */
.fixed-back-button-container {
  position: fixed;
  top: 20px;
  left: 20px;
  z-index: 1000;
  animation: slideInFromLeft 0.5s ease-out;
}

@keyframes slideInFromLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Back Button Container (legacy - for pages not yet updated) */
.back-button-container {
  margin-bottom: 20px;
}

/* Header */
.all-visitors-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30px;
  flex-wrap: wrap;
  gap: 15px;
  position: relative;
}

.all-visitors-header h1 {
  color: #fff;
  font-size: 2.5rem;
  font-weight: 900;
  text-shadow: 0 4px 8px rgba(0,0,0,0.3);
  display: flex;
  align-items: center;
  gap: 15px;
  margin: 0;
  text-align: center;
}

/* Fixed Back Button Styling */
.fixed-back-button {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: #fff;
  padding: 14px 22px;
  border-radius: 50px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 10px;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  backdrop-filter: blur(20px);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.3),
    0 4px 16px rgba(255, 255, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  font-family: 'Montserrat', sans-serif;
  text-transform: uppercase;
  letter-spacing: 1px;
  position: relative;
  overflow: hidden;
}

.fixed-back-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.fixed-back-button:hover::before {
  left: 100%;
}

.fixed-back-button:hover {
  background: linear-gradient(135deg, rgba(255, 45, 85, 0.2) 0%, rgba(75, 0, 130, 0.2) 100%);
  border-color: rgba(255, 45, 85, 0.5);
  transform: translateY(-3px) scale(1.05);
  box-shadow:
    0 12px 40px rgba(255, 45, 85, 0.3),
    0 6px 20px rgba(75, 0, 130, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.fixed-back-button:active {
  transform: translateY(-1px) scale(1.02);
}

/* Legacy back button styling (for pages not yet updated) */
.back-button {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  color: #fff;
  padding: 12px 20px;
  border-radius: 12px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.back-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-2px);
}

.geo-loading {
  color: #fff;
  font-size: 0.9rem;
  opacity: 0.8;
  animation: pulse 2s infinite;
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
}

/* Controls */
.visitors-controls {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.control-group label {
  color: #fff;
  font-weight: 600;
  font-size: 0.9rem;
}

.control-group select {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  color: #fff;
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 0.9rem;
  backdrop-filter: blur(10px);
}

.control-group select option {
  background: #333;
  color: #fff;
}

.show-all-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: 2px solid rgba(255, 255, 255, 0.2);
  color: #fff;
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.show-all-btn:hover {
  background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.show-all-btn.active {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
}

.show-all-btn.active:hover {
  background: linear-gradient(135deg, #ee5a24 0%, #ff6b6b 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 107, 107, 0.5);
}

/* Summary Stats */
.visitors-summary {
  display: flex;
  gap: 20px;
  margin-bottom: 30px;
  flex-wrap: wrap;
}

.summary-stat {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  padding: 15px 20px;
  border-radius: 12px;
  color: #fff;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
  backdrop-filter: blur(10px);
}

/* Visitors Grid */
.all-visitors-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

/* Show More Button */
.show-more-container {
  display: flex;
  justify-content: center;
  margin: 30px 0;
  position: relative;
  z-index: 10;
}

.show-more-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 15px 30px;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.show-more-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
  background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
}

.show-more-button:active {
  transform: translateY(0);
}

.visitor-card {
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.visitor-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
}

.visitor-card:hover::before {
  opacity: 1;
}

.visitor-card:hover {
  transform: translateY(-5px);
  border-color: rgba(255, 255, 255, 0.4);
  box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.visitor-card > * {
  position: relative;
  z-index: 2;
}

/* Visitor Card Header */
.visitor-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.visitor-location {
  display: flex;
  align-items: center;
  gap: 12px;
}

.country-flag {
  font-size: 2rem;
  line-height: 1;
  display: inline-block;
  vertical-align: middle;
  position: relative;

  /* Enhanced emoji font stack for better flag rendering across all platforms */
  font-family:
    "Noto Color Emoji",
    "Apple Color Emoji",
    "Segoe UI Emoji",
    "Segoe UI Symbol",
    "Symbola",
    "DejaVu Sans",
    "Android Emoji",
    "EmojiSymbols",
    "EmojiOne Mozilla",
    "Twemoji Mozilla",
    "JoyPixels",
    "Liberation Sans",
    "Helvetica Neue",
    "Arial Unicode MS",
    sans-serif;

  /* Force emoji rendering and improve compatibility */
  font-variant-emoji: emoji;
  font-feature-settings: "liga" off, "kern" off;
  text-rendering: optimizeSpeed;
  -webkit-font-smoothing: subpixel-antialiased;
  -moz-osx-font-smoothing: auto;
  font-smooth: never;

  /* Ensure visibility and proper rendering */
  opacity: 1;
  visibility: visible;
  color: transparent;
  text-shadow: 0 0 0 transparent;

  /* Force hardware acceleration for better emoji rendering */
  transform: translateZ(0);
  will-change: transform;
  backface-visibility: hidden;

  /* Prevent text selection issues */
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;

  /* Ensure proper spacing and sizing */
  margin-right: 8px;
  min-width: 2rem;
  max-width: 2rem;
  text-align: center;

  /* Prevent emoji from being affected by parent color */
  -webkit-text-fill-color: initial;
  -webkit-background-clip: initial;

  /* Ensure consistent rendering across browsers */
  word-break: keep-all;
  white-space: nowrap;
  overflow: visible;
}

.flag-fallback {
  display: none;
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.9);
  font-family: 'Courier New', monospace;
  font-weight: 600;
  position: absolute;
  bottom: -18px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.9);
  padding: 3px 6px;
  border-radius: 4px;
  white-space: nowrap;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(5px);
  z-index: 10;
  pointer-events: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

/* Show fallback text on hover for debugging and accessibility */
.country-flag:hover .flag-fallback {
  display: block;
  animation: fadeInFlag 0.2s ease-in-out;
}

/* Add animation for smooth appearance */
@keyframes fadeInFlag {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(5px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

/* Enhanced flag container for better emoji support */
.country-flag::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: transparent;
  z-index: -1;
}

/* Ensure flag is always visible even if emoji fails */
.country-flag:empty::after {
  content: '🌍';
  display: inline-block;
  font-size: inherit;
}

.location-info {
  display: flex;
  flex-direction: column;
}

.country-name {
  color: #fff;
  font-weight: 700;
  font-size: 1.1rem;
}

.city-name {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 4px;
}

.region-info {
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.85rem;
  font-style: italic;
}

.geo-error-indicator {
  font-size: 0.8rem;
  margin-left: 4px;
  opacity: 0.7;
  cursor: help;
}

.visitor-ip {
  color: rgba(255, 255, 255, 0.8);
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  background: rgba(0, 0, 0, 0.2);
  padding: 4px 8px;
  border-radius: 6px;
}

/* Visitor Stats */
.visitor-stats {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #fff;
  font-size: 0.9rem;
}

.stat-icon {
  color: rgba(255, 255, 255, 0.7);
}

/* Visitor Details */
.visitor-details {
  margin-bottom: 15px;
}

.most-visited {
  color: #fff;
  margin-bottom: 8px;
  font-size: 0.9rem;
}

.visit-times {
  display: flex;
  justify-content: space-between;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.8rem;
}

/* Visitor Card Footer */
.visitor-card-footer {
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.8rem;
  text-align: center;
  padding-top: 10px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Loading and Error States */
.all-visitors-loading,
.all-visitors-error {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 50vh;
  color: #fff;
  font-size: 1.2rem;
  text-align: center;
}

.no-visitors {
  text-align: center;
  color: rgba(255, 255, 255, 0.7);
  font-size: 1.1rem;
  padding: 40px;
}



/* Visitor Chart Section Styles */
.visitor-chart-section {
  margin: 50px 0 30px 0;
  position: relative;
  z-index: 3;
}

.chart-title {
  font-size: 2rem;
  color: #fff;
  margin-bottom: 30px;
  display: flex;
  align-items: center;
  gap: 15px;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
  text-align: center;
  justify-content: center;
}

.chart-title svg {
  color: #FF2D55;
  font-size: 1.8rem;
}

/* Metrics Explanation */
.metrics-explanation {
  display: flex;
  gap: 30px;
  margin-bottom: 20px;
  padding: 15px 20px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.metric-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.metric-indicator {
  width: 20px;
  height: 4px;
  border-radius: 2px;
}

.metric-indicator.unique-visitors {
  background: #FF2D55;
  box-shadow: 0 0 10px rgba(255, 45, 85, 0.5);
}

.metric-indicator.total-visits {
  background: #4B0082;
  box-shadow: 0 0 10px rgba(75, 0, 130, 0.5);
}

.metric-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.metric-details strong {
  color: #fff;
  font-size: 0.9rem;
  font-weight: 600;
}

.metric-details span {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.8rem;
  line-height: 1.3;
}

.chart-container {
  background: linear-gradient(135deg, rgba(0,0,0,0.8) 0%, rgba(75,0,130,0.1) 100%);
  border-radius: 25px;
  padding: 30px;
  border: 2px solid rgba(255,255,255,0.1);
  backdrop-filter: blur(15px);
  box-shadow:
    0 15px 35px rgba(75,0,130,0.2),
    0 8px 20px rgba(255,45,85,0.15),
    0 3px 10px rgba(0,0,0,0.3);
  margin-bottom: 20px;
  position: relative;
  overflow-x: auto;
  overflow-y: hidden;
  /* Custom scrollbar styling */
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 45, 85, 0.5) rgba(255, 255, 255, 0.1);
}

/* Webkit scrollbar styling for Chrome/Safari */
.chart-container::-webkit-scrollbar {
  height: 8px;
}

.chart-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
}

.chart-container::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #4B0082 0%, #FF2D55 100%);
  border-radius: 10px;
  transition: background 0.3s ease;
}

.chart-container::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #FF2D55 0%, #4B0082 100%);
}

/* Chart wrapper for horizontal scrolling */
.chart-scroll-wrapper {
  min-width: 800px; /* Minimum width for proper chart display */
  width: 100%;
  min-height: 400px;
  overflow-x: auto;
}

/* Mobile-specific chart improvements */
@media (max-width: 768px) {
  .chart-container {
    padding: 20px 15px;
    /* Show scrollbar on mobile for better UX */
    scrollbar-width: auto;
  }

  .chart-container::-webkit-scrollbar {
    height: 12px;
  }

  .chart-scroll-wrapper {
    min-width: 150%; /* Force wider content on mobile */
  }

  /* Add visual hint for scrolling */
  .chart-container::after {
    content: "← Swipe to scroll →";
    position: absolute;
    bottom: 10px;
    right: 15px;
    color: rgba(255, 255, 255, 0.6);
    font-size: 12px;
    pointer-events: none;
    animation: fadeInOut 3s infinite;
  }
}

@keyframes fadeInOut {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.8; }
}

/* Desktop scroll hint */
@media (min-width: 769px) {
  .chart-container::after {
    content: "Use mouse wheel or drag to scroll";
    position: absolute;
    bottom: 10px;
    right: 15px;
    color: rgba(255, 255, 255, 0.4);
    font-size: 11px;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .chart-container:hover::after {
    opacity: 1;
  }
}

.chart-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, #4B0082 0%, #FF2D55 100%);
  border-radius: 25px 25px 0 0;
}

.chart-legend {
  display: flex;
  justify-content: center;
  gap: 30px;
  margin-top: 20px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #fff;
  font-weight: 500;
  font-size: 1rem;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .all-visitors-container {
    padding: 15px;
  }
  
  .all-visitors-header h1 {
    font-size: 2rem;
  }
  
  .all-visitors-grid {
    grid-template-columns: 1fr;
  }
  
  .visitors-controls {
    flex-direction: column;
  }
  
  .visitors-summary {
    flex-direction: column;
  }
  
  .visitor-card-header {
    flex-direction: column;
    gap: 10px;
  }
  
  .visitor-stats {
    justify-content: space-between;
  }

  .country-flag {
    font-size: 1.8rem;
    min-width: 1.8rem;
    max-width: 1.8rem;
    margin-right: 6px;

    /* Enhanced mobile emoji rendering */
    -webkit-text-size-adjust: 100%;
    text-size-adjust: 100%;

    /* Ensure proper touch interaction */
    touch-action: manipulation;
  }

  .flag-fallback {
    font-size: 0.7rem;
    bottom: -16px;
    padding: 2px 4px;
  }



  .chart-title {
    font-size: 1.6rem;
    flex-direction: column;
    gap: 10px;
  }

  .chart-container {
    padding: 20px;
    margin: 0 -10px 20px -10px;
  }

  .chart-legend {
    flex-direction: column;
    gap: 15px;
    align-items: center;
  }

  /* Mobile metrics explanation */
  .metrics-explanation {
    flex-direction: column;
    gap: 15px;
    padding: 12px 15px;
  }

  .metric-info {
    gap: 10px;
  }

  .metric-details strong {
    font-size: 0.85rem;
  }

  .metric-details span {
    font-size: 0.75rem;
  }

  /* Fixed back button mobile adjustments */
  .fixed-back-button-container {
    top: 15px;
    left: 15px;
  }

  .fixed-back-button {
    padding: 12px 18px;
    font-size: 0.9rem;
    border-radius: 40px;
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .all-visitors-header {
    flex-direction: column;
    align-items: center;
  }

  .geo-loading {
    position: static;
    transform: none;
    margin-top: 10px;
  }
  
  .visitor-stats {
    flex-direction: column;
    gap: 8px;
  }

  .country-flag {
    font-size: 1.5rem;
    min-width: 1.5rem;
    max-width: 1.5rem;
    margin-right: 5px;

    /* Optimize for small screens */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  .flag-fallback {
    font-size: 0.65rem;
    bottom: -14px;
    padding: 1px 3px;
  }

  /* Fixed back button small mobile adjustments */
  .fixed-back-button-container {
    top: 10px;
    left: 10px;
  }

  .fixed-back-button {
    padding: 10px 16px;
    font-size: 0.8rem;
    border-radius: 35px;
    gap: 6px;
  }
}
