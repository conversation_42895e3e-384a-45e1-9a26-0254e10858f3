// IP Geolocation utility service
// Using multiple services with fallback

import { logDebug, warnDebug } from './logger';

const GEOLOCATION_CACHE = new Map();
// const CACHE_DURATION = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

/**
 * Get country information for an IP address
 * @deprecated DISABLED: Direct API calls cause CORS errors in production
 * @param {string} ip - IP address to lookup
 * @returns {Promise<Object>} Country information object
 */
export const getCountryFromIP = async (ip) => {
  warnDebug('⚠️ getCountryFromIP is disabled to prevent CORS errors. Use backend geolocation instead.');

  // Return fallback data immediately to prevent CORS issues
  if (ip === '127.0.0.1' || ip === '::1' || ip.startsWith('192.168.') || ip.startsWith('10.') || ip.startsWith('172.')) {
    return {
      country: 'Local',
      country_code: 'LO',
      city: 'Localhost',
      region: 'Local',
      flag: '🏠',
      error: false
    };
  }

  // For all other IPs, return fallback data
  return {
    country: 'Unknown',
    country_code: 'UN',
    city: 'Unknown',
    region: 'Unknown',
    timezone: 'Unknown',
    isp: 'Unknown',
    flag: '🌍',
    error: true,
    errorMessage: 'Direct API calls disabled to prevent CORS errors'
  };
};

/**
 * Get country flag emoji from country code
 * @param {string} countryCode - Two-letter country code
 * @returns {string} Flag emoji
 * Note: This function is commented out as it's not used directly in this file
 * The flag generation logic is implemented in AllVisitorsDetails.js
 */

/**
 * Batch lookup multiple IPs
 * @param {string[]} ips - Array of IP addresses
 * @returns {Promise<Object>} Object with IP as key and country info as value
 */
export const batchGetCountriesFromIPs = async (ips) => {
  logDebug(`Starting batch geolocation lookup for ${ips.length} IPs via backend`);

  // Remove duplicates and filter out empty IPs, and clean comma-separated IPs
  const cleanedIPs = ips.map(ip => {
    if (ip && ip.includes(',')) {
      return ip.split(',')[0].trim();
    }
    return ip;
  });

  const uniqueIPs = [...new Set(cleanedIPs.filter(ip => ip && ip.trim() !== ''))];
  logDebug(`Processing ${uniqueIPs.length} unique IPs after cleaning`);

  if (uniqueIPs.length === 0) {
    logDebug('No valid IPs to process');
    return {};
  }

  // Limit the number of IPs to prevent overwhelming the service
  const maxIPs = 50; // Limit to 50 IPs at once
  if (uniqueIPs.length > maxIPs) {
    logDebug(`Too many IPs (${uniqueIPs.length}), limiting to first ${maxIPs}`);
    uniqueIPs.splice(maxIPs);
  }

  try {
    const token = localStorage.getItem('token');
    if (!token) {
      warnDebug('No authentication token found, using direct geolocation');
      throw new Error('No authentication token found');
    }

    const API_URL = process.env.REACT_APP_API_URL;
    if (!API_URL) {
      warnDebug('API URL not configured, using direct geolocation');
      throw new Error('API URL not configured');
    }

    logDebug('Making backend geolocation request...');

    // Create a timeout controller
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 20000); // 20 second timeout

    const response = await fetch(`${API_URL}/api/admin/geolocation`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({ ips: uniqueIPs }),
      signal: controller.signal
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      // If endpoint returns 404, it means the geolocation endpoint is not deployed
      if (response.status === 404) {
        warnDebug('⚠️ Geolocation endpoint not found (404). Using direct API fallback...');
        throw new Error('Endpoint not found - using fallback');
      }
      throw new Error(`Backend geolocation request failed: ${response.status}`);
    }

    const result = await response.json();

    if (!result.success) {
      throw new Error(result.message || 'Backend geolocation lookup failed');
    }

    logDebug(`Backend geolocation completed. Processed ${result.processedCount} IPs`);
    return result.data;

  } catch (error) {
    // Handle abort errors specifically
    if (error.name === 'AbortError') {
      warnDebug('🔄 Backend geolocation request timed out, using fallback data');
    } else {
      warnDebug('🔄 Backend geolocation failed, using fallback data:', error.message);
    }

    // IMPORTANT: Do NOT make direct API calls from frontend due to CORS and rate limiting
    // Instead, return fallback data for all IPs
    logDebug('⚠️ Using fallback geolocation data due to backend failure or rate limiting');

    const fallbackResults = {};

    // Create fallback data for all IPs
    uniqueIPs.forEach(ip => {
      // Try to determine country from IP pattern (basic heuristics)
      let country = 'Unknown';
      let countryCode = 'UN';
      let flag = '🌍';

      // Enhanced IP geolocation heuristics for common ranges
      if (ip.startsWith('127.') || ip === '::1') {
        country = 'Local';
        countryCode = 'LO';
        flag = '🏠';
      } else if (ip.startsWith('192.168.') || ip.startsWith('10.') || ip.startsWith('172.')) {
        country = 'Local Network';
        countryCode = 'LO';
        flag = '🏠';
      } else if (ip.startsWith('41.227.') || ip.startsWith('41.230.') || ip.startsWith('41.225.')) {
        // Tunisia-specific IP ranges
        country = 'Tunisia';
        countryCode = 'TN';
        flag = '🇹🇳';
      } else if (ip.startsWith('102.') && (
                 // Original ranges
                 ip.startsWith('102.169.') || ip.startsWith('102.24.') || ip.startsWith('102.172.') ||
                 // Expanded 102.157.x.x range (covers ************** and similar)
                 ip.startsWith('102.157.') || ip.startsWith('102.158.') || ip.startsWith('102.159.') ||
                 ip.startsWith('102.160.') || ip.startsWith('102.161.') || ip.startsWith('102.162.') ||
                 ip.startsWith('102.163.') || ip.startsWith('102.164.') || ip.startsWith('102.165.') ||
                 ip.startsWith('102.166.') || ip.startsWith('102.167.') || ip.startsWith('102.168.') ||
                 ip.startsWith('102.170.') || ip.startsWith('102.171.') || ip.startsWith('102.173.') ||
                 ip.startsWith('102.174.') || ip.startsWith('102.175.') || ip.startsWith('102.176.') ||
                 ip.startsWith('102.177.') || ip.startsWith('102.178.') || ip.startsWith('102.179.') ||
                 ip.startsWith('102.180.') || ip.startsWith('102.181.') || ip.startsWith('102.182.') ||
                 ip.startsWith('102.183.') || ip.startsWith('102.184.') || ip.startsWith('102.185.') ||
                 ip.startsWith('102.186.') || ip.startsWith('102.187.') || ip.startsWith('102.188.') ||
                 ip.startsWith('102.189.') || ip.startsWith('102.190.') || ip.startsWith('102.191.') ||
                 ip.startsWith('102.192.') || ip.startsWith('102.193.') || ip.startsWith('102.194.') ||
                 ip.startsWith('102.195.') || ip.startsWith('102.196.') || ip.startsWith('102.197.') ||
                 ip.startsWith('102.198.') || ip.startsWith('102.199.') || ip.startsWith('102.200.') ||
                 // Additional Tunisia ranges
                 ip.startsWith('102.140.') || ip.startsWith('102.141.') || ip.startsWith('102.142.') ||
                 ip.startsWith('102.143.') || ip.startsWith('102.144.') || ip.startsWith('102.145.') ||
                 ip.startsWith('102.146.') || ip.startsWith('102.147.') || ip.startsWith('102.148.') ||
                 ip.startsWith('102.149.') || ip.startsWith('102.150.') || ip.startsWith('102.151.') ||
                 ip.startsWith('102.152.') || ip.startsWith('102.153.') || ip.startsWith('102.154.') ||
                 ip.startsWith('102.155.') || ip.startsWith('102.156.')
                 )) {
        // More Tunisia-specific IP ranges - MASSIVELY EXPANDED to cover 102.157.x.x
        country = 'Tunisia';
        countryCode = 'TN';
        flag = '🇹🇳';
      } else if (ip.startsWith('197.14.')) {
        // Another Tunisia IP range
        country = 'Tunisia';
        countryCode = 'TN';
        flag = '🇹🇳';
      } else if (ip.startsWith('176.143.')) {
        // European IP range (possibly France/Europe)
        country = 'France';
        countryCode = 'FR';
        flag = '🇫🇷';
      } else if (ip.startsWith('8.8.') || ip.startsWith('1.1.')) {
        // Google/Cloudflare DNS
        country = 'United States';
        countryCode = 'US';
        flag = '🇺🇸';
      }

      fallbackResults[ip] = {
        country: country,
        country_code: countryCode,
        city: 'Unknown',
        region: 'Unknown',
        timezone: 'Unknown',
        isp: 'Unknown',
        flag: flag,
        error: true,
        errorMessage: 'Geolocation service unavailable (rate limited or CORS blocked)'
      };
    });

    logDebug(`🔄 Fallback completed with heuristic data for ${Object.keys(fallbackResults).length} IPs`);
    return fallbackResults;
  }
};

/**
 * Clear the geolocation cache
 */
export const clearGeolocationCache = () => {
  GEOLOCATION_CACHE.clear();
};

/**
 * Get cache statistics
 * @returns {Object} Cache statistics
 */
export const getCacheStats = () => {
  return {
    size: GEOLOCATION_CACHE.size,
    entries: Array.from(GEOLOCATION_CACHE.keys())
  };
};
