import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { FaBriefcase, FaEye, FaClock, FaUsers, FaChartLine, FaExternalLinkAlt, FaBuilding, FaCalendarAlt } from 'react-icons/fa';
import './ExperienceProjectsAnalytics.css';
import { preemptiveWakeup } from '../utils/backendWakeup';
import { formatDuration } from '../hooks/useVisitorTracking';
import { API_CONFIG } from '../config/apiConfig';
import { safeFetch, isExtensionError } from '../utils/extensionErrorHandler';
import { jobsData } from '../data/jobsData';
import { findExperienceProject, isValidExperienceProject } from '../config/projectsConfig';
import FixedBackButton from './FixedBackButton';

const ExperienceProjectsAnalytics = () => {
  const [analytics, setAnalytics] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const navigate = useNavigate();

  // Helper function to get job data using centralized project configuration
  const getJobData = (jobSlug, jobTitle) => {
    // Use centralized configuration to find the project
    const experienceProject = findExperienceProject(jobSlug, jobTitle);

    if (experienceProject) {
      // Find the corresponding job data by ID
      return jobsData.find(job => job.id === experienceProject.id);
    }

    // Fallback to original logic if not found in configuration
    let jobData = jobsData.find(job => job.slug === jobSlug);
    if (!jobData && jobTitle) {
      jobData = jobsData.find(job =>
        job.title.toLowerCase().includes(jobTitle.toLowerCase()) ||
        jobTitle.toLowerCase().includes(job.title.toLowerCase())
      );
    }

    return jobData;
  };

  useEffect(() => {
    fetchAnalytics();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      setError('');

      // Wake up backend before attempting to fetch analytics
      console.log('Waking up backend...');
      await preemptiveWakeup();

      const token = localStorage.getItem('token');
      if (!token) {
        navigate('/admin/login');
        return;
      }

      console.log('Fetching experience analytics from:', API_CONFIG.ENDPOINTS.EXPERIENCE_PROJECTS_ANALYTICS);

      const response = await safeFetch(API_CONFIG.ENDPOINTS.EXPERIENCE_PROJECTS_ANALYTICS, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('Response status:', response.status);

      if (response.status === 401) {
        localStorage.removeItem('token');
        navigate('/admin/login');
        return;
      }

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Response error:', errorText);
        throw new Error(`Failed to fetch analytics (${response.status}): ${errorText}`);
      }

      const data = await response.json();
      console.log('Analytics data received:', data);

      // Additional frontend filtering using centralized project configuration
      if (data.success && data.data) {
        const filteredData = {
          ...data,
          data: data.data.filter(project => {
            return isValidExperienceProject(project.jobSlug, project.jobTitle);
          }).slice(0, 3) // Ensure maximum of 3 projects
        };

        console.log('Filtered experience projects:', filteredData.data.length);
        setAnalytics(filteredData);
      } else {
        setAnalytics(data);
      }
    } catch (err) {
      // Don't show errors for extension-related issues
      if (!isExtensionError(err)) {
        console.error('Analytics fetch error:', err);
        setError(`Error loading analytics: ${err.message}`);
      } else {
        console.log('Extension error suppressed in ExperienceProjectsAnalytics:', err.message);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleJobClick = (jobSlug) => {
    if (jobSlug && jobSlug !== 'unknown') {
      const url = `${window.location.origin}/job/${jobSlug}`;
      window.open(url, '_blank');
    }
  };

  const handleBackClick = () => {
    navigate('/admin/dashboard');
  };

  if (loading) {
    return (
      <div className="experience-analytics-container">
        <div className="loading-spinner">
          <div className="spinner"></div>
          <p>Loading Experience Analytics...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="experience-analytics-container">
        <div className="error-message">
          <h3>Error Loading Analytics</h3>
          <p>{error}</p>
          <button onClick={fetchAnalytics} className="retry-button">
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="experience-analytics-container">
      {/* Fixed Back Button */}
      <FixedBackButton onClick={handleBackClick} text="Back to Dashboard" />

      <div className="analytics-header">
        <h1><FaBriefcase /> Experience Projects Analytics</h1>
        <p>Detailed analytics for professional experience section interactions</p>
      </div>

      {analytics && (
        <>
          {/* Summary Cards */}
          <div className="analytics-summary">
            <div className="summary-card">
              <FaBriefcase className="summary-icon" />
              <div className="summary-content">
                <h3>Total Projects</h3>
                <div className="summary-value">{analytics.totalProjects}</div>
              </div>
            </div>
            <div className="summary-card">
              <FaEye className="summary-icon" />
              <div className="summary-content">
                <h3>Total Views</h3>
                <div className="summary-value">{analytics.summary.totalViews}</div>
              </div>
            </div>
            <div className="summary-card">
              <FaClock className="summary-icon" />
              <div className="summary-content">
                <h3>Total Time</h3>
                <div className="summary-value">{formatDuration(analytics.summary.totalDuration)}</div>
              </div>
            </div>
            <div className="summary-card">
              <FaChartLine className="summary-icon" />
              <div className="summary-content">
                <h3>Avg Views/Project</h3>
                <div className="summary-value">{analytics.summary.avgViewsPerProject}</div>
              </div>
            </div>
          </div>

          {/* Projects List */}
          <div className="projects-analytics-list">
            <h2>Project Performance</h2>
            {analytics.data && analytics.data.length > 0 ? (
              <div className="projects-grid">
                {analytics.data.map((project, index) => {
                  const jobData = getJobData(project.jobSlug, project.jobTitle);

                  return (
                    <div key={`${project.jobSlug || project.jobTitle || 'unknown'}-${index}`} className="project-analytics-card">
                      <div className="project-visual-header">
                        {jobData && jobData.logo && (
                          <div className="company-logo-container">
                            <img
                              src={jobData.logo}
                              alt={jobData.logoAlt || `${jobData.company} Logo`}
                              className="company-logo-analytics"
                              onError={(e) => {
                                e.target.style.display = 'none';
                              }}
                            />
                          </div>
                        )}
                        <div className="project-header">
                          <h3 className="project-title">{project.jobTitle}</h3>
                          {jobData && (
                            <div className="project-meta">
                              <div className="company-info">
                                <FaBuilding className="meta-icon" />
                                <span>{jobData.company}</span>
                              </div>
                              <div className="duration-info">
                                <FaCalendarAlt className="meta-icon" />
                                <span>{jobData.duration}</span>
                              </div>
                            </div>
                          )}
                          <button
                            className="view-project-btn"
                            onClick={() => handleJobClick(project.jobSlug)}
                            disabled={!project.jobSlug || project.jobSlug === 'unknown'}
                          >
                            <FaExternalLinkAlt /> View Project
                          </button>
                        </div>
                      </div>

                      {/* Project Summary */}
                      {jobData && jobData.summary && (
                        <div className="project-summary">
                          <p>{jobData.summary}</p>
                        </div>
                      )}

                      <div className="project-stats">
                        <div className="stats-grid">
                          <div className="stat-item primary">
                            <FaEye className="stat-icon" />
                            <div className="stat-content">
                              <span className="stat-value">{project.totalViews}</span>
                              <span className="stat-label">Total Views</span>
                            </div>
                          </div>
                          <div className="stat-item primary">
                            <FaUsers className="stat-icon" />
                            <div className="stat-content">
                              <span className="stat-value">{project.uniqueVisitorCount}</span>
                              <span className="stat-label">Unique Visitors</span>
                            </div>
                          </div>
                          <div className="stat-item secondary">
                            <FaClock className="stat-icon" />
                            <div className="stat-content">
                              <span className="stat-value">{formatDuration(project.totalDuration)}</span>
                              <span className="stat-label">Total Duration</span>
                            </div>
                          </div>
                          <div className="stat-item secondary">
                            <FaClock className="stat-icon" />
                            <div className="stat-content">
                              <span className="stat-value">{formatDuration(project.avgDuration)}</span>
                              <span className="stat-label">Avg Duration</span>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="project-details">
                        <div className="detail-item">
                          <strong>Project Slug:</strong>
                          <span className="detail-value">{project.jobSlug || 'N/A'}</span>
                        </div>
                        <div className="detail-item">
                          <strong>Last Visit:</strong>
                          <span className="detail-value">
                            {project.lastVisit ?
                              new Date(project.lastVisit).toLocaleString() :
                              'Never'
                            }
                          </span>
                        </div>
                        {jobData && jobData.companyLink && (
                          <div className="detail-item">
                            <strong>Company Link:</strong>
                            <a
                              href={jobData.companyLink}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="company-link"
                            >
                              {jobData.companyLink}
                            </a>
                          </div>
                        )}
                      </div>

                      {project.recentInteractions && project.recentInteractions.length > 0 && (
                        <div className="recent-interactions">
                          <h4>Recent Interactions</h4>
                          <div className="interactions-list">
                            {project.recentInteractions.slice(0, 5).map((interaction, idx) => (
                              <div key={idx} className="interaction-item">
                                <div className="interaction-info">
                                  <span className="interaction-ip">{interaction.ip}</span>
                                  <span className="interaction-type">{interaction.interactionType || 'view'}</span>
                                </div>
                                <div className="interaction-meta">
                                  <span className="interaction-time">
                                    {new Date(interaction.timestamp).toLocaleString()}
                                  </span>
                                  <span className="interaction-duration">
                                    {formatDuration(interaction.duration)}
                                  </span>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            ) : (
              <div className="no-data">
                <p>No experience project analytics data available yet.</p>
                <p>Data will appear once users start interacting with the experience section.</p>
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
};

export default ExperienceProjectsAnalytics;
