import React, { memo } from 'react';
import { useVisitorTracking } from '../hooks/useVisitorTracking';

const SkillsTicker = memo(() => {
  const { ref } = useVisitorTracking('skills-ticker', {
    threshold: 0.7,
    minDuration: 2
  });

  const skills = ['UI/UX DESIGN', 'GRAPHIC DESIGN', 'WEB DEVELOPMENT', 'BRANDING', 'WEBSITE DESIGN', 'DIGITAL CREATIVITY', 'E-COMMERCE SOLUTIONS', 'CUSTOM DEVELOPMENT'];
  const skillsText = skills.join(' • ');

  return (
    <div className="skills-ticker" ref={ref}>
      <div className="ticker-track">
        <span>{skillsText}</span>
        {/* Duplicate for infinite scroll */}
        <span>{skillsText}</span>
      </div>
    </div>
  );
});

SkillsTicker.displayName = 'SkillsTicker';

export default SkillsTicker;
