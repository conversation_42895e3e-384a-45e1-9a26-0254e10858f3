// Flag Display Test Utility
// This utility helps test and debug flag display functionality

/**
 * Test flag generation for various country codes
 */
export const testFlagGeneration = () => {
  const testCases = [
    { code: 'TN', expected: '🇹🇳', name: 'Tunisia' },
    { code: 'US', expected: '🇺🇸', name: 'United States' },
    { code: 'FR', expected: '🇫🇷', name: 'France' },
    { code: 'DE', expected: '🇩🇪', name: 'Germany' },
    { code: 'GB', expected: '🇬🇧', name: 'United Kingdom' },
    { code: 'CA', expected: '🇨🇦', name: 'Canada' },
    { code: 'JP', expected: '🇯🇵', name: 'Japan' },
    { code: 'AU', expected: '🇦🇺', name: 'Australia' },
    { code: 'UN', expected: '🌍', name: 'Unknown' },
    { code: 'LO', expected: '🏠', name: 'Local' },
  ];

  console.log('🧪 Testing Flag Generation:');
  console.log('============================');

  testCases.forEach(({ code, expected, name }) => {
    const generated = generateFlag(code);
    const passed = generated === expected;
    console.log(`${passed ? '✅' : '❌'} ${code} (${name}): ${generated} ${passed ? '' : `(expected: ${expected})`}`);
  });
};

/**
 * Generate flag from country code (same logic as in AllVisitorsDetails)
 */
const generateFlag = (countryCode) => {
  if (!countryCode || countryCode.length !== 2) return '🌍';

  try {
    const normalizedCode = countryCode.toUpperCase().trim();
    
    const specialFlags = {
      'UN': '🌍',
      'LO': '🏠',
      'XX': '🌍',
      'ZZ': '🌍'
    };

    if (specialFlags[normalizedCode]) {
      return specialFlags[normalizedCode];
    }

    const codePoints = normalizedCode
      .split('')
      .map(char => 127397 + char.charCodeAt(0));

    const flag = String.fromCodePoint(...codePoints);
    
    if (flag && flag.length >= 2 && !flag.includes('�')) {
      return flag;
    }
    
    return '🌍';
  } catch (error) {
    console.error('Flag generation error for code:', countryCode, error);
    return '🌍';
  }
};

/**
 * Test IP-based flag detection
 */
export const testIPFlagDetection = () => {
  const testIPs = [
    { ip: '*************', expected: '🇹🇳', name: 'Tunisia IP' },
    { ip: '**************', expected: '🇹🇳', name: 'Tunisia IP (102.157.x.x)' },
    { ip: '*************', expected: '🇫🇷', name: 'France IP' },
    { ip: '*******', expected: '🇺🇸', name: 'Google DNS' },
    { ip: '***********', expected: '🏠', name: 'Local IP' },
    { ip: '127.0.0.1', expected: '🏠', name: 'Localhost' },
  ];

  console.log('\n🌐 Testing IP-based Flag Detection:');
  console.log('===================================');

  testIPs.forEach(({ ip, expected, name }) => {
    const detected = detectFlagFromIP(ip);
    const passed = detected === expected;
    console.log(`${passed ? '✅' : '❌'} ${ip} (${name}): ${detected} ${passed ? '' : `(expected: ${expected})`}`);
  });
};

/**
 * Detect flag from IP (same logic as in AllVisitorsDetails)
 */
const detectFlagFromIP = (ip) => {
  if (!ip || typeof ip !== 'string') return '🌍';

  // Tunisia IP ranges
  if (ip.startsWith('41.227.') || ip.startsWith('41.230.') || ip.startsWith('41.225.') ||
      ip.startsWith('41.226.') || ip.startsWith('41.228.') || ip.startsWith('41.229.') ||
      ip.startsWith('102.157.') || ip.startsWith('102.158.') || ip.startsWith('102.159.') ||
      ip.startsWith('102.160.') || ip.startsWith('102.161.') || ip.startsWith('102.162.') ||
      ip.startsWith('197.14.') || ip.startsWith('196.200.') || ip.startsWith('196.201.') ||
      ip.startsWith('197.0.') || ip.startsWith('197.1.') || ip.startsWith('197.2.')) {
    return '🇹🇳';
  }
  
  // France/Europe IP ranges
  if (ip.startsWith('176.143.') || ip.startsWith('85.') || ip.startsWith('90.') ||
      ip.startsWith('91.') || ip.startsWith('92.') || ip.startsWith('93.') ||
      ip.startsWith('94.') || ip.startsWith('95.')) {
    return '🇫🇷';
  }
  
  // US IP ranges
  if (ip.startsWith('8.8.') || ip.startsWith('1.1.') || ip.startsWith('4.4.') ||
      ip.startsWith('208.67.') || ip.startsWith('64.6.') || ip.startsWith('199.85.')) {
    return '🇺🇸';
  }
  
  // Local/Private IP ranges
  if (ip.startsWith('127.') || ip.startsWith('192.168.') || 
      ip.startsWith('10.') || ip.startsWith('172.16.') ||
      ip.startsWith('172.17.') || ip.startsWith('172.18.') ||
      ip.startsWith('172.19.') || ip.startsWith('172.20.') ||
      ip.startsWith('172.21.') || ip.startsWith('172.22.') ||
      ip.startsWith('172.23.') || ip.startsWith('172.24.') ||
      ip.startsWith('172.25.') || ip.startsWith('172.26.') ||
      ip.startsWith('172.27.') || ip.startsWith('172.28.') ||
      ip.startsWith('172.29.') || ip.startsWith('172.30.') ||
      ip.startsWith('172.31.') || ip === '::1') {
    return '🏠';
  }

  return '🌍';
};

/**
 * Run all tests
 */
export const runAllFlagTests = () => {
  console.log('🚀 Running Flag Display Tests...\n');
  testFlagGeneration();
  testIPFlagDetection();
  console.log('\n✨ Flag tests completed!');
};

// Auto-run tests if this file is imported in development
if (process.env.NODE_ENV === 'development') {
  // Uncomment the line below to run tests automatically
  // runAllFlagTests();
}
